const ARK_API_KEY = '9d9113ca-c9a0-49bc-b06a-62b5973a7b6a'; // 请替换为您的实际 API 密钥
// const PROXY_BASE_URL = 'http://localhost:3000'; // 指向您的代理服务器地址
const DIRECT_API_BASE_URL = 'https://ark.cn-beijing.volces.com/api/v3'; // 豆包API直接地址

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ChatCompletionResponse {
  choices: Array<{
    message: ChatMessage;
  }>;
}

interface ImageGenerationResponse {
  data: Array<{
    url: string;
  }>;
}

export async function generateStory(messages: ChatMessage[], model: string = "doubao-lite-4k"): Promise<string> {
  try {
    const response = await uni.request({
      url: `${DIRECT_API_BASE_URL}/chat/completions`, // 直接调用豆包API
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ARK_API_KEY}`
      },
      data: {
        model: model,
        messages: messages,
        stream: false
      }
    });

    if (response.statusCode === 200 && response.data) {
      const data = response.data as ChatCompletionResponse;
      return data.choices[0].message.content;
    } else {
      console.error('Error generating story:', response);
      return 'Failed to generate story.';
    }
  } catch (error) {
    console.error('Network error generating story:', error);
    return 'Network error.';
  }
}

export async function generateImage(prompt: string, model: string = "doubao-seedream-3-0-t2i-250415"): Promise<string> {
  try {
    const response = await uni.request({
      url: `${DIRECT_API_BASE_URL}/images/generations`, // 直接调用豆包API
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ARK_API_KEY}`
      },
      data: {
        model: model,
        prompt: prompt,
        response_format: "url",
        size: "1024x1024"
      }
    });

    if (response.statusCode === 200 && response.data) {
      const data = response.data as ImageGenerationResponse;
      return data.data[0].url;
    } else {
      console.error('Error generating image:', response);
      return ''; // Return empty string on error
    }
  } catch (error) {
    console.error('Network error generating image:', error);
    return ''; // Return empty string on error
  }
}